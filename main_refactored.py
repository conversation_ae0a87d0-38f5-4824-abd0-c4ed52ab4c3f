"""
Refactored main.py using unified modules for clean and consistent code.
This demonstrates the improved architecture with:
- Unified data processing with feature engineering
- Model factory for consistent model creation
- Unified trainer for all model types
- Comprehensive evaluation with all metrics
- Clean separation of concerns
"""

from arguments import parse_args
from data_processing import DataProcessor
from model_factory import ModelFactory
from trainer import UnifiedTrainer
from evaluator import ModelEvaluator
from plotting import plot_losses, plot_test_acc, plot_enhanced_metrics
import torch
import pandas as pd


def main():
    """Main function with clean, unified approach."""
    # Setup
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    args = parse_args()

    print(f"Training {args.model_name} model")
    print(f"Device: {device}")
    print(f"Test fold: {args.test_fold}/{args.kfold}")
    print(f"Feature engineering: {args.apply_feature_engineering}")
    print(f"Feature scaling: {args.apply_scaling}")

    # Data processing
    print("\nProcessing data...")

    # For sklearn models, we don't normalize labels as they handle it internally
    apply_label_scaling = ModelFactory.is_pytorch_model(args.model_name)

    data_processor = DataProcessor(
        apply_feature_engineering=args.apply_feature_engineering,
        apply_feature_scaling=args.apply_scaling,
        apply_label_scaling=apply_label_scaling  # Only scale labels for PyTorch models
    )

    # Determine data format based on model type
    if ModelFactory.is_pytorch_model(args.model_name):
        train_data, test_data = data_processor.process_data(args, return_format='dataloader')
        print(f"Created PyTorch DataLoaders")
    else:
        X_train, y_train, X_test, y_test = data_processor.process_data(args, return_format='numpy')
        train_data = (X_train, y_train)
        test_data = (X_test, y_test)
        print(f"Created numpy arrays: X_train {X_train.shape}, y_train {y_train.shape}")

    # Model creation
    print(f"\nCreating {args.model_name} model...")
    model = ModelFactory.create_model(args.model_name, args, device)
    print(f"Model created successfully")

    # Training
    print(f"\nTraining {args.model_name} model...")
    trainer = UnifiedTrainer(model, args.model_name, args, device)
    train_losses, test_losses, test_acc, enhanced_metrics = trainer.train(train_data, test_data)

    # Evaluation summary
    evaluator = ModelEvaluator()
    evaluator.print_metrics(enhanced_metrics, args.model_name)

    # Determine epochs for plotting
    epochs = len(train_losses) if len(train_losses) > 1 else 1

    # Plotting
    print(f"\nGenerating plots...")
    plot_losses(train_losses, test_losses, epochs, args)
    plot_test_acc(test_acc, epochs, args)
    plot_enhanced_metrics(enhanced_metrics, args)

    # Save results
    print(f"\nSaving results...")
    save_results(train_losses, test_losses, test_acc, enhanced_metrics, args)

    print(f"\nTraining completed successfully!")
    print(f"Results saved to: ./res/{args.model_name}-{args.test_fold}.csv")
    print(f"Plots saved to: ./plots/")


def save_results(train_losses, test_losses, test_acc, enhanced_metrics, args):
    """Save results in the same format as the original main.py."""
    res = []
    res.append(train_losses)
    res.append(test_losses)
    res.append(test_acc[0])  # power accuracy
    res.append(test_acc[1])  # area accuracy
    res.append([enhanced_metrics.get('power_mse', 0)])
    res.append([enhanced_metrics.get('area_mse', 0)])
    res.append([enhanced_metrics.get('overall_mse', 0)])
    res.append([enhanced_metrics.get('power_r2', 0)])
    res.append([enhanced_metrics.get('area_r2', 0)])
    res.append([enhanced_metrics.get('overall_r2', 0)])
    res.append([enhanced_metrics.get('power_rmse', 0)])
    res.append([enhanced_metrics.get('area_rmse', 0)])
    res.append([enhanced_metrics.get('overall_rmse', 0)])
    res.append([enhanced_metrics.get('power_mape', 0)])
    res.append([enhanced_metrics.get('area_mape', 0)])
    res.append([enhanced_metrics.get('overall_mape', 0)])

    index_labels = [
        'train losses', 'test losses', 'power acc', 'area acc',
        'power mse', 'area mse', 'overall mse',
        'power r2', 'area r2', 'overall r2',
        'power rmse', 'area rmse', 'overall rmse',
        'power mape', 'area mape', 'overall mape'
    ]

    pd.DataFrame(res, index=index_labels).to_csv(f'./res/{args.model_name}-{args.test_fold}.csv')


def run_cross_validation(model_name, args_override=None):
    """
    Run 5-fold cross-validation for a given model.

    Args:
        model_name: Name of the model to test
        args_override: Dictionary of argument overrides
    """
    print(f"\n{'='*60}")
    print(f"RUNNING 5-FOLD CROSS-VALIDATION FOR {model_name.upper()}")
    print('='*60)

    results = []

    for fold in range(5):
        print(f"\nFold {fold + 1}/5")
        print("-" * 30)

        # Parse arguments and override model name and fold
        args = parse_args()
        args.model_name = model_name
        args.test_fold = fold

        # Apply any additional overrides
        if args_override:
            for key, value in args_override.items():
                setattr(args, key, value)

        # Run training for this fold
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

        # Data processing
        data_processor = DataProcessor(
            apply_feature_engineering=args.apply_feature_engineering,
            apply_scaling=args.apply_scaling
        )

        if ModelFactory.is_pytorch_model(args.model_name):
            train_data, test_data = data_processor.process_data(args, return_format='dataloader')
        else:
            X_train, y_train, X_test, y_test = data_processor.process_data(args, return_format='numpy')
            train_data = (X_train, y_train)
            test_data = (X_test, y_test)

        # Model creation and training
        model = ModelFactory.create_model(args.model_name, args, device)
        trainer = UnifiedTrainer(model, args.model_name, args, device)
        _, _, _, enhanced_metrics = trainer.train(train_data, test_data)

        results.append(enhanced_metrics)

        print(f"Fold {fold + 1} - R²: {enhanced_metrics['overall_r2']:.4f}, "
              f"MSE: {enhanced_metrics['overall_mse']:.6f}")

    # Calculate average metrics
    avg_metrics = {}
    for key in results[0].keys():
        if isinstance(results[0][key], (int, float)):
            avg_metrics[key] = sum(result[key] for result in results) / len(results)

    print(f"\n{'='*60}")
    print(f"CROSS-VALIDATION SUMMARY FOR {model_name.upper()}")
    print('='*60)
    print(f"Average R²: {avg_metrics['overall_r2']:.4f}")
    print(f"Average MSE: {avg_metrics['overall_mse']:.6f}")
    print(f"Average RMSE: {avg_metrics['overall_rmse']:.6f}")
    print(f"Average MAPE: {avg_metrics['overall_mape']:.4f}")
    print('='*60)

    return results, avg_metrics


if __name__ == '__main__':
    main()
